<?php
/**
 * Script de diagnóstico para el error de funciones_aws.php
 * Ayuda a identificar problemas con archivos WSFE
 */

// Incluir configuraciones
require_once '../../../acc/acc.php';

// Función para verificar archivo
function verificar_archivo($ruta_completa, $extension) {
    echo "<h3>Verificando archivo: {$extension}</h3>";
    echo "<strong>Ruta completa:</strong> {$ruta_completa}<br>";
    
    // Verificar si el archivo existe
    if (file_exists($ruta_completa)) {
        echo "✅ El archivo existe<br>";
        
        // Verificar permisos de lectura
        if (is_readable($ruta_completa)) {
            echo "✅ El archivo es legible<br>";
            
            // Verificar tamaño del archivo
            $tamaño = filesize($ruta_completa);
            echo "<strong>Tamaño:</strong> {$tamaño} bytes<br>";
            
            if ($tamaño > 0) {
                echo "✅ El archivo no está vacío<br>";
                
                // Intentar abrir el archivo
                $handle = fopen($ruta_completa, 'r');
                if ($handle !== false) {
                    echo "✅ fopen() funciona correctamente<br>";
                    fclose($handle);
                } else {
                    echo "❌ fopen() falló - Error: " . error_get_last()['message'] . "<br>";
                }
            } else {
                echo "⚠️ El archivo está vacío<br>";
            }
        } else {
            echo "❌ El archivo no es legible<br>";
            echo "<strong>Permisos:</strong> " . substr(sprintf('%o', fileperms($ruta_completa)), -4) . "<br>";
        }
        
        // Información adicional del archivo
        echo "<strong>Propietario:</strong> " . fileowner($ruta_completa) . "<br>";
        echo "<strong>Grupo:</strong> " . filegroup($ruta_completa) . "<br>";
        echo "<strong>Última modificación:</strong> " . date('Y-m-d H:i:s', filemtime($ruta_completa)) . "<br>";
        
    } else {
        echo "❌ El archivo NO existe<br>";
        
        // Verificar si el directorio existe
        $directorio = dirname($ruta_completa);
        if (is_dir($directorio)) {
            echo "✅ El directorio padre existe: {$directorio}<br>";
            echo "<strong>Permisos del directorio:</strong> " . substr(sprintf('%o', fileperms($directorio)), -4) . "<br>";
            
            // Listar archivos en el directorio
            echo "<strong>Archivos en el directorio:</strong><br>";
            $archivos = scandir($directorio);
            foreach ($archivos as $archivo) {
                if ($archivo != '.' && $archivo != '..') {
                    echo "- {$archivo}<br>";
                }
            }
        } else {
            echo "❌ El directorio padre NO existe: {$directorio}<br>";
        }
    }
    
    echo "<hr>";
}

// Obtener el CUIT desde la URL o usar uno de prueba
$cuit = isset($_GET['cuit']) ? $_GET['cuit'] : '30708653833';

echo "<h1>Diagnóstico de archivos WSFE</h1>";
echo "<h2>CUIT: {$cuit}</h2>";

// Mostrar configuración
echo "<h3>Configuración actual:</h3>";
echo "<strong>PATH_WSFE:</strong> " . PATH_WSFE . "<br>";
echo "<strong>Usuario PHP:</strong> " . get_current_user() . "<br>";
echo "<strong>UID:</strong> " . getmyuid() . "<br>";
echo "<strong>GID:</strong> " . getmygid() . "<br>";
echo "<hr>";

// Verificar directorio base
echo "<h3>Verificando directorio base WSFE</h3>";
if (is_dir(PATH_WSFE)) {
    echo "✅ El directorio PATH_WSFE existe<br>";
    echo "<strong>Permisos:</strong> " . substr(sprintf('%o', fileperms(PATH_WSFE)), -4) . "<br>";
} else {
    echo "❌ El directorio PATH_WSFE NO existe<br>";
}
echo "<hr>";

// Verificar cada extensión
$extensiones = array('key', 'req', 'ini');
foreach ($extensiones as $extension) {
    $ruta_completa = PATH_WSFE . $cuit . '.' . $extension;
    verificar_archivo($ruta_completa, $extension);
}

// Simular el error original
echo "<h3>Simulando el error original</h3>";
foreach ($extensiones as $extension) {
    $ruta_completa = PATH_WSFE . $cuit . '.' . $extension;
    echo "<strong>Extensión {$extension}:</strong> ";
    
    $handle = fopen($ruta_completa, 'r');
    if ($handle === false) {
        echo "❌ fopen() devolvió FALSE - Este es el problema!<br>";
        echo "Error: " . error_get_last()['message'] . "<br>";
    } else {
        echo "✅ fopen() funcionó correctamente<br>";
        fclose($handle);
    }
}

echo "<hr>";
echo "<h3>Comandos útiles para ejecutar en el servidor:</h3>";
echo "<code>ls -la " . PATH_WSFE . "</code><br>";
echo "<code>ls -la " . PATH_WSFE . $cuit . ".*</code><br>";
echo "<code>whoami</code><br>";
echo "<code>id</code><br>";

?>
