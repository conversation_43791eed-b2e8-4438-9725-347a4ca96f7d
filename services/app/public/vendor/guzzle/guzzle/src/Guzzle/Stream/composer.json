{"name": "guzzle/stream", "description": "Guzzle stream wrapper component", "homepage": "http://guzzlephp.org/", "keywords": ["stream", "component", "guzzle"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2", "guzzle/common": "self.version"}, "suggest": {"guzzle/http": "To convert Guzzle request objects to PHP streams"}, "autoload": {"psr-0": {"Guzzle\\Stream": ""}}, "target-dir": "Guzzle/Stream", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}