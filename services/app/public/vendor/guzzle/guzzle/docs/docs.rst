.. title:: Guzzle | PHP HTTP client and framework for consuming RESTful web services

====================
Guzzle Documentation
====================

Getting started
---------------

.. toctree::
   :maxdepth: 1

   getting-started/overview
   getting-started/installation
   getting-started/faq

The HTTP client
---------------

.. toctree::
   :maxdepth: 2

   http-client/client
   http-client/request
   http-client/response
   http-client/entity-bodies
   http-client/http-redirects
   http-client/uri-templates

Plugins
-------

.. toctree::
   :maxdepth: 1

   plugins/plugins-overview
   plugins/creating-plugins
   plugins/async-plugin
   plugins/backoff-plugin
   plugins/cache-plugin
   plugins/cookie-plugin
   plugins/curl-auth-plugin
   plugins/history-plugin
   plugins/log-plugin
   plugins/md5-validator-plugin
   plugins/mock-plugin
   plugins/oauth-plugin

The web service client
----------------------

.. toctree::
   :maxdepth: 1

   webservice-client/webservice-client
   webservice-client/using-the-service-builder
   webservice-client/guzzle-service-descriptions
   batching/batching
   iterators/resource-iterators
   iterators/guzzle-iterators

Testing
-------

.. toctree::
   :maxdepth: 2

   testing/unit-testing

API Docs
--------

`Read the API docs <http://guzzlephp.org/api/index.html>`_
