==========
Log plugin
==========

Use the ``Guzzle\Plugin\Log\LogPlugin`` to view all data sent over the wire, including entity bodies and redirects.

.. code-block:: php

    use Guzzle\Http\Client;
    use Guzzle\Log\Zf1LogAdapter;
    use Guzzle\Plugin\Log\LogPlugin;
    use Guzzle\Log\MessageFormatter;

    $client = new Client('http://www.test.com/');

    $adapter = new Zf1LogAdapter(
        new \Zend_Log(new \Zend_Log_Writer_Stream('php://output'))
    );
    $logPlugin = new LogPlugin($adapter, MessageFormatter::DEBUG_FORMAT);

    // Attach the plugin to the client, which will in turn be attached to all
    // requests generated by the client
    $client->addSubscriber($logPlugin);

    $response = $client->get('http://google.com')->send();

The code sample above wraps a ``Zend_Log`` object using a ``Guzzle\Log\Zf1LogAdapter``. After attaching the plugin to
the client, all data sent over the wire will be logged to stdout.

The first argument of the LogPlugin's constructor accepts a ``Guzzle\Log\LogAdapterInterface`` object. This object is
an adapter that allows you to use the logging capabilities of your favorite log implementation. The second argument of
the constructor accepts a ``Guzzle\Log\MessageFormatter`` or a log messaged format string. The format string uses
variable substitution and allows you to define the log data that is important to your application. The different
variables that can be injected are as follows:

================== ====================================================================================
Variable           Substitution
================== ====================================================================================
{request}          Full HTTP request message
{response}         Full HTTP response message
{ts}               Timestamp
{host}             Host of the request
{method}           Method of the request
{url}              URL of the request
{host}             Host of the request
{protocol}         Request protocol
{version}          Protocol version
{resource}         Resource of the request (path + query + fragment)
{port}             Port of the request
{hostname}         Hostname of the machine that sent the request
{code}             Status code of the response (if available)
{phrase}           Reason phrase of the response  (if available)
{curl_error}       Curl error message (if available)
{curl_code}        Curl error code (if available)
{curl_stderr}      Curl standard error (if available)
{connect_time}     Time in seconds it took to establish the connection (if available)
{total_time}       Total transaction time in seconds for last transfer (if available)
{req_header_*}     Replace `*` with the lowercased name of a request header to add to the message
{res_header_*}     Replace `*` with the lowercased name of a response header to add to the message
{req_body}         Request body
{res_body}         Response body
================== ====================================================================================

The LogPlugin has a helper method that can be used when debugging that will output the full HTTP request and
response of a transaction:

.. code-block:: php

    $client->addSubscriber(LogPlugin::getDebugPlugin());
