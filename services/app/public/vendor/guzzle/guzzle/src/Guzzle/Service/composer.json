{"name": "guzzle/service", "description": "Guzzle service component for abstracting RESTful web services", "homepage": "http://guzzlephp.org/", "keywords": ["web service", "webservice", "REST", "guzzle"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2", "guzzle/cache": "self.version", "guzzle/http": "self.version", "guzzle/inflection": "self.version"}, "autoload": {"psr-0": {"Guzzle\\Service": ""}}, "target-dir": "Guzzle/Service", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}