<?php
/**
 * Script CLI para diagnosticar el problema de WSFE
 * Uso: php debug_wsfe_cli.php [cuit]
 */

// Incluir configuraciones
require_once '../../../acc/acc.php';

// Obtener CUIT desde argumentos o usar el del error
$cuit = isset($argv[1]) ? $argv[1] : '30708653833';

echo "=== DIAGNÓSTICO WSFE ===\n";
echo "CUIT: $cuit\n";
echo "PATH_WSFE: " . PATH_WSFE . "\n";
echo "Usuario: " . get_current_user() . "\n";
echo "UID: " . getmyuid() . "\n";
echo "GID: " . getmygid() . "\n\n";

// Verificar directorio
echo "Verificando directorio base:\n";
if (is_dir(PATH_WSFE)) {
    echo "✓ Directorio existe\n";
    echo "  Permisos: " . substr(sprintf('%o', fileperms(PATH_WSFE)), -4) . "\n";
} else {
    echo "✗ Directorio NO existe\n";
}

echo "\nVerificando archivos:\n";
$extensiones = array('key', 'req', 'ini');
$problemas = array();

foreach ($extensiones as $extension) {
    $archivo = PATH_WSFE . $cuit . '.' . $extension;
    echo "\n$extension: $archivo\n";
    
    if (file_exists($archivo)) {
        echo "  ✓ Existe\n";
        echo "  Tamaño: " . filesize($archivo) . " bytes\n";
        echo "  Permisos: " . substr(sprintf('%o', fileperms($archivo)), -4) . "\n";
        echo "  Propietario: " . fileowner($archivo) . "\n";
        echo "  Grupo: " . filegroup($archivo) . "\n";
        
        if (is_readable($archivo)) {
            echo "  ✓ Legible\n";
            
            // Probar fopen
            $handle = fopen($archivo, 'r');
            if ($handle === false) {
                echo "  ✗ fopen() FALLÓ\n";
                $problemas[] = "$extension: fopen() falló";
            } else {
                echo "  ✓ fopen() OK\n";
                fclose($handle);
            }
        } else {
            echo "  ✗ NO legible\n";
            $problemas[] = "$extension: no legible";
        }
    } else {
        echo "  ✗ NO existe\n";
        $problemas[] = "$extension: no existe";
    }
}

echo "\n=== RESUMEN ===\n";
if (empty($problemas)) {
    echo "✓ No se encontraron problemas\n";
} else {
    echo "✗ Problemas encontrados:\n";
    foreach ($problemas as $problema) {
        echo "  - $problema\n";
    }
}

echo "\n=== COMANDOS ÚTILES ===\n";
echo "ls -la " . PATH_WSFE . "\n";
echo "ls -la " . PATH_WSFE . $cuit . ".*\n";
echo "find " . PATH_WSFE . " -name '$cuit.*' -ls\n";
echo "whoami && id\n";

?>
