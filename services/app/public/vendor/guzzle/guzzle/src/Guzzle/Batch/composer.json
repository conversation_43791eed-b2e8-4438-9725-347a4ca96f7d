{"name": "guzzle/batch", "description": "Guzzle batch component for batching requests, commands, or custom transfers", "homepage": "http://guzzlephp.org/", "keywords": ["batch", "HTTP", "REST", "guzzle"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2", "guzzle/common": "self.version"}, "autoload": {"psr-0": {"Guzzle\\Batch": ""}}, "suggest": {"guzzle/http": "self.version", "guzzle/service": "self.version"}, "target-dir": "Guzzle/Batch", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}