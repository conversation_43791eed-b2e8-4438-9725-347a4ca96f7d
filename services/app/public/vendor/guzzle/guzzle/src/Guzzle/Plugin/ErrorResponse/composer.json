{"name": "guzzle/plugin-error-response", "description": "Guzzle errorResponse plugin for creating error exceptions based on a service description", "homepage": "http://guzzlephp.org/", "keywords": ["plugin", "guzzle"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2", "guzzle/service": "self.version"}, "autoload": {"psr-0": {"Guzzle\\Plugin\\ErrorResponse": ""}}, "target-dir": "Guzzle/Plugin/ErrorResponse", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}